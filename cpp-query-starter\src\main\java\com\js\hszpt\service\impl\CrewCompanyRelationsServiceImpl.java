package com.js.hszpt.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.js.hszpt.entity.CrewCompanyInfo;
import com.js.hszpt.entity.CrewCompanyType;
import com.js.hszpt.entity.CrewEmployment;
import com.js.hszpt.entity.CrewEmpShipService;
import com.js.hszpt.mapper.CrewCompanyInfoMapper;
import com.js.hszpt.mapper.CrewCompanyTypeMapper;
import com.js.hszpt.mapper.CrewEmploymentMapper;
import com.js.hszpt.mapper.CrewEmpShipServiceMapper;
import com.js.hszpt.service.CrewCompanyRelationsService;
import com.js.hszpt.vo.CompanyRelationsStatisticsVO;
import com.js.hszpt.vo.CompanyRelationsDetailVO;
import com.js.hszpt.vo.CompanyTypeVO;
import com.js.hszpt.vo.EmploymentPeriodVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 船员公司关系服务实现类
 */
@Slf4j
@Service
public class CrewCompanyRelationsServiceImpl implements CrewCompanyRelationsService {

    @Autowired
    private CrewEmploymentMapper crewEmploymentMapper;

    @Autowired
    private CrewCompanyInfoMapper crewCompanyInfoMapper;

    @Autowired
    private CrewEmpShipServiceMapper crewEmpShipServiceMapper;

    @Autowired
    private CrewCompanyTypeMapper crewCompanyTypeMapper;

    @Override
    public CompanyRelationsStatisticsVO getCompanyRelationsStatistics(String seafarerId) {
        log.info("获取船员公司关系统计信息，船员ID：{}", seafarerId);

        try {
            // 调用Mapper获取统计信息
            log.info("开始调用getCompanyRelationsStatistics，船员ID：{}", seafarerId);
            Map<String, Object> statistics = crewEmploymentMapper.getCompanyRelationsStatistics(seafarerId);
            log.info("SQL查询返回结果：{}", statistics);

            CompanyRelationsStatisticsVO result = new CompanyRelationsStatisticsVO();
            if (statistics != null) {
                // 修复类型转换问题：Long/Double/BigDecimal -> Double
                Object totalCompaniesObj = statistics.get("totalCompanies");
                Object totalYearsObj = statistics.get("totalYears");
                Object currentCompaniesObj = statistics.get("currentCompanies");

                log.info("原始数据 - totalCompanies: {} (类型: {}), totalYears: {} (类型: {}), currentCompanies: {} (类型: {})", 
                    totalCompaniesObj, totalCompaniesObj != null ? totalCompaniesObj.getClass().getSimpleName() : "null",
                    totalYearsObj, totalYearsObj != null ? totalYearsObj.getClass().getSimpleName() : "null",
                    currentCompaniesObj, currentCompaniesObj != null ? currentCompaniesObj.getClass().getSimpleName() : "null");

                result.setTotalCompanies(totalCompaniesObj instanceof Long ? ((Long) totalCompaniesObj).intValue()
                        : totalCompaniesObj instanceof Integer ? (Integer) totalCompaniesObj : 0);
                // totalYears 支持BigDecimal/Double/Integer/Long
                if (totalYearsObj instanceof java.math.BigDecimal) {
                    result.setTotalYears(((java.math.BigDecimal) totalYearsObj).doubleValue());
                } else if (totalYearsObj instanceof Double) {
                    result.setTotalYears((Double) totalYearsObj);
                } else if (totalYearsObj instanceof Float) {
                    result.setTotalYears(((Float) totalYearsObj).doubleValue());
                } else if (totalYearsObj instanceof Long) {
                    result.setTotalYears(((Long) totalYearsObj).doubleValue());
                } else if (totalYearsObj instanceof Integer) {
                    result.setTotalYears(((Integer) totalYearsObj).doubleValue());
                } else if (totalYearsObj != null) {
                    try {
                        result.setTotalYears(Double.parseDouble(totalYearsObj.toString()));
                    } catch (Exception e) {
                        result.setTotalYears(0.0);
                    }
                } else {
                    result.setTotalYears(0.0);
                }
                result.setCurrentCompanies(currentCompaniesObj instanceof Long ? ((Long) currentCompaniesObj).intValue()
                        : currentCompaniesObj instanceof Integer ? (Integer) currentCompaniesObj : 0);
                
                log.info("转换后结果 - totalCompanies: {}, totalYears: {}, currentCompanies: {}", 
                    result.getTotalCompanies(), result.getTotalYears(), result.getCurrentCompanies());
            } else {
                // 如果没有数据，设置默认值
                log.info("统计结果为null，设置默认值");
                result.setTotalCompanies(0);
                result.setTotalYears(0.0);
                result.setCurrentCompanies(0);
            }

            return result;
        } catch (Exception e) {
            log.error("获取船员公司关系统计信息失败，船员ID：{}", seafarerId, e);
            throw new RuntimeException("获取船员公司关系统计信息失败", e);
        }
    }

    @Override
    public List<CompanyRelationsDetailVO> getCompanyRelationsList(String seafarerId, List<String> companyTypes) {
        log.info("获取船员公司履历详细列表，船员ID：{}，公司类型：{}", seafarerId, companyTypes);

        try {
            // 根据条件查询任职关系
            List<CrewEmployment> employments;
            if (companyTypes != null && !companyTypes.isEmpty()) {
                log.info("根据船员ID和公司类型查询任职关系，船员ID：{}，公司类型：{}", seafarerId, companyTypes);
                employments = crewEmploymentMapper.selectByCrewIdAndCompanyTypes(seafarerId, companyTypes);
            } else {
                log.info("根据船员ID查询所有任职关系，船员ID：{}", seafarerId);
                employments = crewEmploymentMapper.selectByCrewId(seafarerId);
            }

            log.info("查询到的任职关系数量：{}", employments.size());
            if (employments.isEmpty()) {
                log.warn("未找到船员ID为 {} 的任职关系数据", seafarerId);
                return new ArrayList<>();
            }

            // 获取所有相关的公司ID
            List<String> companyIds = employments.stream()
                    .map(CrewEmployment::getCompanyId)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("提取到的公司ID列表：{}", companyIds);

            // 查询公司信息
            log.info("开始查询公司信息，公司ID数量：{}", companyIds.size());
            List<CrewCompanyInfo> companies = crewCompanyInfoMapper.selectByCompanyIds(companyIds);
            log.info("查询到的公司信息数量：{}", companies.size());
            
            if (companies.isEmpty()) {
                log.warn("未找到对应的公司信息，公司ID列表：{}", companyIds);
            }
            
            Map<String, CrewCompanyInfo> companyMap = companies.stream()
                    .collect(Collectors.toMap(CrewCompanyInfo::getCompanyId, company -> company));

            // 查询船舶服务信息
            List<String> employmentIds = employments.stream()
                    .map(CrewEmployment::getEmploymentId)
                    .collect(Collectors.toList());
            
            log.info("提取到的任职关系ID列表：{}", employmentIds);
            
            log.info("开始查询船舶服务信息，任职关系ID数量：{}", employmentIds.size());
            List<CrewEmpShipService> shipServices = crewEmpShipServiceMapper.selectByEmploymentIds(employmentIds);
            log.info("查询到的船舶服务信息数量：{}", shipServices.size());
            
            if (shipServices.isEmpty()) {
                log.warn("未找到对应的船舶服务信息，任职关系ID列表：{}", employmentIds);
            }
            
            Map<String, List<CrewEmpShipService>> shipServiceMap = shipServices.stream()
                    .collect(Collectors.groupingBy(CrewEmpShipService::getEmploymentId));

            // 构建结果
            Map<String, CompanyRelationsDetailVO> resultMap = new HashMap<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            log.info("开始构建公司关系详情，处理 {} 条任职关系", employments.size());
            int processedCount = 0;
            int skippedCount = 0;

            for (CrewEmployment employment : employments) {
                String companyId = employment.getCompanyId();
                CrewCompanyInfo company = companyMap.get(companyId);

                if (company == null) {
                    log.warn("未找到公司信息，跳过任职关系，公司ID：{}，任职关系ID：{}", companyId, employment.getEmploymentId());
                    skippedCount++;
                    continue;
                }

                CompanyRelationsDetailVO detailVO = resultMap.computeIfAbsent(companyId, k -> {
                    CompanyRelationsDetailVO vo = new CompanyRelationsDetailVO();
                    vo.setId(company.getCompanyId());
                    vo.setName(company.getCompanyName());
                    vo.setShortName(company.getCompanyShortName());
                    vo.setType(employment.getCompanyType()); // 从employment中获取公司类型
                    vo.setAddress(company.getAddress());
                    vo.setContact(company.getContact());
                    vo.setPeriods(new ArrayList<>());
                    return vo;
                });

                // 构建任职期间信息
                EmploymentPeriodVO periodVO = new EmploymentPeriodVO();
                periodVO.setStartDate(dateFormat.format(employment.getStartDate()));
                if (employment.getEndDate() != null) {
                    periodVO.setEndDate(dateFormat.format(employment.getEndDate()));
                    periodVO.setIsActive(false);
                } else {
                    periodVO.setIsActive(true);
                }

                // 获取船舶服务信息
                List<CrewEmpShipService> services = shipServiceMap.get(employment.getEmploymentId());
                if (services != null) {
                    periodVO.setShips(services.stream()
                            .map(CrewEmpShipService::getShipName)
                            .distinct()
                            .collect(Collectors.toList()));
                    periodVO.setPositions(services.stream()
                            .map(CrewEmpShipService::getPosition)
                            .distinct()
                            .collect(Collectors.toList()));
                } else {
                    periodVO.setShips(new ArrayList<>());
                    periodVO.setPositions(new ArrayList<>());
                }

                detailVO.getPeriods().add(periodVO);
                processedCount++;
            }

            log.info("处理完成，成功处理：{} 条，跳过：{} 条，最终公司数量：{}", processedCount, skippedCount, resultMap.size());

            resultMap.values().forEach(detailVO -> {
                List<EmploymentPeriodVO> periods = detailVO.getPeriods();
                if (CollUtil.isEmpty(periods) || periods.size() == 1) {
                    return;
                }

            });

            // 对每个公司的任职期间按开始时间倒序排序
            for (CompanyRelationsDetailVO company : resultMap.values()) {
                company.getPeriods().sort((p1, p2) -> {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        Date d1 = sdf.parse(p1.getStartDate());
                        Date d2 = sdf.parse(p2.getStartDate());
                        return d2.compareTo(d1); // 倒序排序，最新的在前
                    } catch (Exception e) {
                        return 0;
                    }
                });
            }

            // 将公司按最晚开始时间倒序排序（最新任职的公司在前）
            List<CompanyRelationsDetailVO> sortedList = new ArrayList<>(resultMap.values());
            sortedList.sort((c1, c2) -> {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    // 获取每个公司最晚的开始时间（最近的任职）
                    String startDate1 = c1.getPeriods().stream()
                            .map(EmploymentPeriodVO::getStartDate)
                            .max(String::compareTo)
                            .orElse("1900-01-01");
                    String startDate2 = c2.getPeriods().stream()
                            .map(EmploymentPeriodVO::getStartDate)
                            .max(String::compareTo)
                            .orElse("1900-01-01");

                    Date d1 = sdf.parse(startDate1);
                    Date d2 = sdf.parse(startDate2);
                    return d2.compareTo(d1); // 倒序排序，最新的在前
                } catch (Exception e) {
                    return 0;
                }
            });

            log.info("最终返回公司关系详情列表，公司数量：{}", sortedList.size());
            return sortedList;
        } catch (Exception e) {
            log.error("获取船员公司履历详细列表失败，船员ID：{}", seafarerId, e);
            throw new RuntimeException("获取船员公司履历详细列表失败", e);
        }
    }

    @Override
    public List<CompanyTypeVO> getCompanyTypes() {
        log.info("获取公司类型字典");

        try {
            List<CrewCompanyType> companyTypes = crewCompanyTypeMapper.selectAllCompanyTypes();

            return companyTypes.stream()
                    .map(type -> {
                        CompanyTypeVO vo = new CompanyTypeVO();
                        vo.setValue(type.getTypeCode());
                        vo.setLabel(type.getTypeName());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取公司类型字典失败", e);
            throw new RuntimeException("获取公司类型字典失败", e);
        }
    }
}